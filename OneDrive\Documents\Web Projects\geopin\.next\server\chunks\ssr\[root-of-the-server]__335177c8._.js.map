{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/geopin/components/banner/banner.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { X } from \"lucide-react\";\r\n\r\nexport default function Banner({\r\n  onStateChange,\r\n}: {\r\n  onStateChange?: (open: boolean) => void;\r\n}) {\r\n  const [bannerOpen, setBannerOpen] = useState(true);\r\n\r\n  // Notify parent of state change\r\n  useEffect(() => {\r\n    if (onStateChange) onStateChange(bannerOpen);\r\n  }, [bannerOpen, onStateChange]);\r\n\r\n  return (\r\n    <div\r\n      id=\"banner\"\r\n      className={`bg-banner flex justify-center transition-all duration-300 ease-in-out overflow-hidden ${\r\n        bannerOpen ? \"h-12\" : \"h-0\"\r\n      }`}\r\n      role=\"alert\"\r\n    >\r\n      <div className=\"flex justify-between max-w-[var(--max-width)] mx-auto w-full\">\r\n        <p className=\"max-w-[var(--max-width)] mx-auto text-primary-white px-3 py-2\">\r\n          Banner Bar Text Here\r\n        </p>\r\n        <button onClick={() => setBanner<PERSON>pen(false)}>\r\n          <X className=\"text-primary-white\" size={32} />\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAKe,SAAS,OAAO,EAC7B,aAAa,EAGd;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,qQAAQ,EAAC;IAE7C,gCAAgC;IAChC,IAAA,sQAAS,EAAC;QACR,IAAI,eAAe,cAAc;IACnC,GAAG;QAAC;QAAY;KAAc;IAE9B,qBACE,kSAAC;QACC,IAAG;QACH,WAAW,CAAC,sFAAsF,EAChG,aAAa,SAAS,OACtB;QACF,MAAK;kBAEL,cAAA,kSAAC;YAAI,WAAU;;8BACb,kSAAC;oBAAE,WAAU;8BAAgE;;;;;;8BAG7E,kSAAC;oBAAO,SAAS,IAAM,cAAc;8BACnC,cAAA,kSAAC,qPAAC;wBAAC,WAAU;wBAAqB,MAAM;;;;;;;;;;;;;;;;;;;;;;AAKlD", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/geopin/components/layout/navbar/desktop_nav/desktop_nav.tsx"], "sourcesContent": ["type DesktopNavProps = {\r\n  children?: React.ReactNode;\r\n};\r\n\r\nexport default function DesktopNav({ children }: DesktopNavProps) {\r\n  return <nav>{children}</nav>;\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAIe,SAAS,WAAW,EAAE,QAAQ,EAAmB;IAC9D,qBAAO,kSAAC;kBAAK;;;;;;AACf", "debugId": null}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/geopin/components/off_canvas/off_canvas.button.tsx"], "sourcesContent": ["\"use client\";\r\nimport { OffCanvasSide } from \"./off_canvas\";\r\nimport { X } from \"lucide-react\";\r\n\r\ntype OffCanvasButtonProps = {\r\n  onClick: () => void;\r\n  side?: OffCanvasSide;\r\n};\r\n\r\nexport default function OffCanvasButton({\r\n  onClick,\r\n  side = \"left\",\r\n}: OffCanvasButtonProps) {\r\n  return (\r\n    <div className={`flex justify-${side === \"left\" ? \"end\" : \"start\"} mb-3`}>\r\n      <button onClick={onClick} type=\"button\">\r\n        <X className=\"text-black\" size={32} />\r\n      </button>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AASe,SAAS,gBAAgB,EACtC,OAAO,EACP,OAAO,MAAM,EACQ;IACrB,qBACE,kSAAC;QAAI,WAAW,CAAC,aAAa,EAAE,SAAS,SAAS,QAAQ,QAAQ,KAAK,CAAC;kBACtE,cAAA,kSAAC;YAAO,SAAS;YAAS,MAAK;sBAC7B,cAAA,kSAAC,qPAAC;gBAAC,WAAU;gBAAa,MAAM;;;;;;;;;;;;;;;;AAIxC", "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/geopin/components/off_canvas/off_canvas.tsx"], "sourcesContent": ["\"use client\";\r\nimport { useEffect, useState } from \"react\";\r\nimport OffCanvasButton from \"./off_canvas.button\";\r\n\r\ntype OffCanvasProps = {\r\n  children: React.ReactNode;\r\n  isOpen: boolean;\r\n  side?: \"left\" | \"right\";\r\n  onClose: () => void;\r\n  className?: string;\r\n};\r\n\r\nexport default function OffCanvas({\r\n  children,\r\n  isOpen,\r\n  side = \"right\",\r\n  className,\r\n  onClose,\r\n}: OffCanvasProps) {\r\n  const [shouldRender, setShouldRender] = useState(false);\r\n  const [visible, setVisible] = useState(false);\r\n\r\n  useEffect(() => {\r\n    if (isOpen) {\r\n      document.body.style.overflow = \"hidden\";\r\n      setShouldRender(true);\r\n      setTimeout(() => setVisible(true), 10);\r\n    } else {\r\n      document.body.style.overflow = \"auto\";\r\n      const timeout = setTimeout(() => setShouldRender(false), 10);\r\n      return () => clearTimeout(timeout);\r\n    }\r\n\r\n    // Cleanup on unmount\r\n    return () => {\r\n      document.body.style.overflow = \"auto\";\r\n    };\r\n  }, [isOpen]);\r\n\r\n  //Close off canvas for transitions\r\n  const closeOffCanvas = () => {\r\n    setVisible(false);\r\n    const timeout = setTimeout(() => {\r\n      onClose();\r\n    }, 300);\r\n\r\n    return () => clearTimeout(timeout);\r\n  };\r\n\r\n  //Don't render if not opened\r\n  if (!shouldRender) return null;\r\n\r\n  return (\r\n    <div\r\n      id=\"off-canvas\"\r\n      className={`h-screen w-screen overflow-hidden fixed top-0 left-0 z-150 ${className}`}\r\n    >\r\n      <div\r\n        className={`h-screen w-screen bg-black fixed top-0 left-0 z-40 transition-opacity duration-300 linear ${\r\n          visible ? \"opacity-50\" : \"opacity-0\"\r\n        }`}\r\n        onClick={closeOffCanvas}\r\n      />\r\n      <div\r\n        className={`absolute top-0 ${\r\n          side === \"left\" ? \"left-0\" : \"right-0\"\r\n        } h-screen w-4/5 bg-white transform transition-transform duration-300 ease-in-out z-50 p-4 max-w-[320px] ${\r\n          isOpen && visible\r\n            ? \"translate-x-0\"\r\n            : side === \"left\"\r\n            ? \"-translate-x-full\"\r\n            : \"translate-x-full\"\r\n        }`}\r\n      >\r\n        <OffCanvasButton onClick={closeOffCanvas} side={side} />\r\n        <div className=\"flex flex-col h-full overflow-y-auto\">{children}</div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport type OffCanvasSide = OffCanvasProps[\"side\"];\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAFA;;;;AAYe,SAAS,UAAU,EAChC,QAAQ,EACR,MAAM,EACN,OAAO,OAAO,EACd,SAAS,EACT,OAAO,EACQ;IACf,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,qQAAQ,EAAC;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,qQAAQ,EAAC;IAEvC,IAAA,sQAAS,EAAC;QACR,IAAI,QAAQ;YACV,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YAC/B,gBAAgB;YAChB,WAAW,IAAM,WAAW,OAAO;QACrC,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YAC/B,MAAM,UAAU,WAAW,IAAM,gBAAgB,QAAQ;YACzD,OAAO,IAAM,aAAa;QAC5B;QAEA,qBAAqB;QACrB,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;IACF,GAAG;QAAC;KAAO;IAEX,kCAAkC;IAClC,MAAM,iBAAiB;QACrB,WAAW;QACX,MAAM,UAAU,WAAW;YACzB;QACF,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B;IAEA,4BAA4B;IAC5B,IAAI,CAAC,cAAc,OAAO;IAE1B,qBACE,kSAAC;QACC,IAAG;QACH,WAAW,CAAC,2DAA2D,EAAE,WAAW;;0BAEpF,kSAAC;gBACC,WAAW,CAAC,0FAA0F,EACpG,UAAU,eAAe,aACzB;gBACF,SAAS;;;;;;0BAEX,kSAAC;gBACC,WAAW,CAAC,eAAe,EACzB,SAAS,SAAS,WAAW,UAC9B,wGAAwG,EACvG,UAAU,UACN,kBACA,SAAS,SACT,sBACA,oBACJ;;kCAEF,kSAAC,gNAAe;wBAAC,SAAS;wBAAgB,MAAM;;;;;;kCAChD,kSAAC;wBAAI,WAAU;kCAAwC;;;;;;;;;;;;;;;;;;AAI/D", "debugId": null}}, {"offset": {"line": 240, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/geopin/components/layout/navbar/toggle_button/toggle_button.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\n\r\nimport { Menu } from \"lucide-react\";\r\n\r\ntype toggle_button_props = {\r\n  onClick: () => void;\r\n  isOpen: boolean;\r\n};\r\n\r\nexport default function ToggleButton({ onClick, isOpen }: toggle_button_props) {\r\n  return (\r\n    <>\r\n      <button onClick={onClick} type=\"button\">\r\n        <Menu className=\"text-black\" size={32} />\r\n      </button>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAIA;AAJA;;;AAWe,SAAS,aAAa,EAAE,OAAO,EAAE,MAAM,EAAuB;IAC3E,qBACE;kBACE,cAAA,kSAAC;YAAO,SAAS;YAAS,MAAK;sBAC7B,cAAA,kSAAC,8PAAI;gBAAC,WAAU;gBAAa,MAAM;;;;;;;;;;;;AAI3C", "debugId": null}}, {"offset": {"line": 273, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/geopin/components/layout/navbar/mobile_nav/mobile_nav.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport OffCanvas from \"@/components/off_canvas/off_canvas\";\r\nimport ToggleButton from \"../toggle_button/toggle_button\";\r\nimport { useState, useEffect } from \"react\";\r\n\r\ntype MobileNavProps = {\r\n  children?: React.ReactNode;\r\n};\r\nexport default function MobileNav({ children }: MobileNavProps) {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n\r\n  //Handle resize, close mobile menu when window is resized\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      if (window.innerWidth >= 1024) {\r\n        setIsOpen(false);\r\n      }\r\n    };\r\n    window.addEventListener(\"resize\", handleResize);\r\n    return () => window.removeEventListener(\"resize\", handleResize);\r\n  }, []);\r\n\r\n  return (\r\n    <>\r\n      <div className=\"flex justify-end\">\r\n        <ToggleButton onClick={() => setIsOpen(!isOpen)} isOpen={isOpen} />\r\n      </div>\r\n      {isOpen && (\r\n        <>\r\n          <OffCanvas\r\n            isOpen={isOpen}\r\n            onClose={() => setIsOpen(false)}\r\n            className=\"blocklg:hidden\"\r\n          >\r\n            <nav>{children}</nav>\r\n          </OffCanvas>\r\n        </>\r\n      )}\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AASe,SAAS,UAAU,EAAE,QAAQ,EAAkB;IAC5D,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,qQAAQ,EAAC;IAErC,yDAAyD;IACzD,IAAA,sQAAS,EAAC;QACR,MAAM,eAAe;YACnB,IAAI,OAAO,UAAU,IAAI,MAAM;gBAC7B,UAAU;YACZ;QACF;QACA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,qBACE;;0BACE,kSAAC;gBAAI,WAAU;0BACb,cAAA,kSAAC,gOAAY;oBAAC,SAAS,IAAM,UAAU,CAAC;oBAAS,QAAQ;;;;;;;;;;;YAE1D,wBACC;0BACE,cAAA,kSAAC,sMAAS;oBACR,QAAQ;oBACR,SAAS,IAAM,UAAU;oBACzB,WAAU;8BAEV,cAAA,kSAAC;kCAAK;;;;;;;;;;;;;;AAMlB", "debugId": null}}, {"offset": {"line": 340, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/geopin/components/layout/navbar/nav/nav.tsx"], "sourcesContent": ["import DesktopNav from \"../desktop_nav/desktop_nav\";\r\nimport MobileNav from \"../mobile_nav/mobile_nav\";\r\n\r\ntype NavProps = {\r\n  children?: React.ReactNode;\r\n};\r\n\r\nexport default function Nav({ children }: NavProps) {\r\n  return (\r\n    <>\r\n      <div\r\n        id=\"mobile-nav\"\r\n        className=\"flex justify-end flex-grow lg:hidden pb-3\"\r\n      >\r\n        <MobileNav>{children}</MobileNav>\r\n      </div>\r\n      <div id=\"desktop-nav\" className=\"hidden lg:flex pb-3\">\r\n        <DesktopNav>{children}</DesktopNav>\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAMe,SAAS,IAAI,EAAE,QAAQ,EAAY;IAChD,qBACE;;0BACE,kSAAC;gBACC,IAAG;gBACH,WAAU;0BAEV,cAAA,kSAAC,0NAAS;8BAAE;;;;;;;;;;;0BAEd,kSAAC;gBAAI,IAAG;gBAAc,WAAU;0BAC9B,cAAA,kSAAC,4NAAU;8BAAE;;;;;;;;;;;;;AAIrB", "debugId": null}}, {"offset": {"line": 390, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/geopin/components/navbar/navbar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect, useState } from \"react\";\r\nimport Link from \"next/link\";\r\nimport Nav from \"../layout/navbar/nav/nav\";\r\n\r\nexport default function Navbar() {\r\n  const [isSticky, setIsSticky] = useState(false);\r\n  const [showNav, setShowNav] = useState(true);\r\n  const [lastScrollY, setLastScrollY] = useState(0); //default window scroll position\r\n\r\n  useEffect(() => {\r\n    const handleScroll = () => {\r\n      const currentScrollY = window.scrollY;\r\n      setIsSticky(currentScrollY > 75);\r\n      if (currentScrollY > lastScrollY && currentScrollY > 75) {\r\n        setShowNav(false); // scrolling down\r\n      } else {\r\n        setShowNav(true); // scrolling up\r\n      }\r\n      setLastScrollY(currentScrollY);\r\n    };\r\n    window.addEventListener(\"scroll\", handleScroll);\r\n    return () => window.removeEventListener(\"scroll\", handleScroll);\r\n  }, [lastScrollY]);\r\n\r\n  return (\r\n    <div\r\n      className={`flex flex-wrap row justify-start items-center px-3 pt-3 gap-4 max-w-[var(--max-width)] mx-auto \r\n            w-full bg-background z-50`}\r\n    >\r\n      <div className=\"flex justify-between w-full\">\r\n        <div>\r\n          <Link href=\"/\">\r\n            <h1 className=\"header font-bold text-2xl\">Geopin</h1>\r\n          </Link>\r\n        </div>\r\n        <Nav />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,qQAAQ,EAAC;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,qQAAQ,EAAC;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,qQAAQ,EAAC,IAAI,gCAAgC;IAEnF,IAAA,sQAAS,EAAC;QACR,MAAM,eAAe;YACnB,MAAM,iBAAiB,OAAO,OAAO;YACrC,YAAY,iBAAiB;YAC7B,IAAI,iBAAiB,eAAe,iBAAiB,IAAI;gBACvD,WAAW,QAAQ,iBAAiB;YACtC,OAAO;gBACL,WAAW,OAAO,eAAe;YACnC;YACA,eAAe;QACjB;QACA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG;QAAC;KAAY;IAEhB,qBACE,kSAAC;QACC,WAAW,CAAC;qCACmB,CAAC;kBAEhC,cAAA,kSAAC;YAAI,WAAU;;8BACb,kSAAC;8BACC,cAAA,kSAAC,2NAAI;wBAAC,MAAK;kCACT,cAAA,kSAAC;4BAAG,WAAU;sCAA4B;;;;;;;;;;;;;;;;8BAG9C,kSAAC,4MAAG;;;;;;;;;;;;;;;;AAIZ", "debugId": null}}]}