{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/geopin/components/button/button.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\ntype ButtonProps = {\r\n  children: React.ReactNode;\r\n  type?: \"button\" | \"submit\" | \"reset\";\r\n  onClick?: () => void;\r\n  className?: string;\r\n};\r\nexport default function Button({\r\n  children,\r\n  type = \"button\",\r\n  onClick,\r\n  className,\r\n}: ButtonProps) {\r\n\r\n    \r\n\r\n  return (\r\n    <button type={type} onClick={onClick} className={`${className}`}>\r\n      {children}\r\n    </button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAQe,SAAS,OAAO,EAC7B,QAAQ,EACR,OAAO,QAAQ,EACf,OAAO,EACP,SAAS,EACG;IAIZ,qBACE,kSAAC;QAAO,MAAM;QAAM,SAAS;QAAS,WAAW,GAAG,WAAW;kBAC5D;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/geopin/components/icons/icon.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/OneDrive/Documents/Web Projects/geopin/components/icons/icon.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/OneDrive/Documents/Web Projects/geopin/components/icons/icon.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,4TAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAoU,GACjW,kGACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/geopin/components/icons/icon.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/OneDrive/Documents/Web Projects/geopin/components/icons/icon.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/OneDrive/Documents/Web Projects/geopin/components/icons/icon.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,4TAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAgT,GAC7U,8EACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 65, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/geopin/components/inline_link/inline_link.tsx"], "sourcesContent": ["import Link from \"next/link\";\r\nimport React from \"react\";\r\n\r\nimport styles from \"./inline_link.module.css\";\r\nimport Icon from \"../icons/icon\";\r\nimport { IconName, iconSizes } from \"../icons/icon_config\";\r\n\r\ntype InlineLinkProps = {\r\n  children: React.ReactNode;\r\n  icon: IconName;\r\n  iconSize: iconSizes;\r\n  iconColor: string;\r\n  href: string;\r\n};\r\n\r\nexport default function InlineLink({\r\n  children,\r\n  icon,\r\n  href,\r\n  iconSize,\r\n  iconColor,\r\n}: InlineLinkProps) {\r\n  return (\r\n    <Link href={href} className=\"w-full\">\r\n      <div className=\"flex gap-4 w-full justify-start\">\r\n        {/*Icon Component */}\r\n        <span>\r\n          <Icon icon={icon} color={iconColor} size={iconSize} />\r\n        </span>\r\n        {/*Hover effect on the text and the arrow */}\r\n        <div className=\"flex gap-4 w-full justify-start hover:text-blue-500 text-xl\">\r\n          <span className=\"\">{children}</span>\r\n          <span className=\"font-bold\">-&gt;</span>\r\n        </div>\r\n      </div>\r\n    </Link>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAIA;;;;AAWe,SAAS,WAAW,EACjC,QAAQ,EACR,IAAI,EACJ,IAAI,EACJ,QAAQ,EACR,SAAS,EACO;IAChB,qBACE,kSAAC,2NAAI;QAAC,MAAM;QAAM,WAAU;kBAC1B,cAAA,kSAAC;YAAI,WAAU;;8BAEb,kSAAC;8BACC,cAAA,kSAAC,2LAAI;wBAAC,MAAM;wBAAM,OAAO;wBAAW,MAAM;;;;;;;;;;;8BAG5C,kSAAC;oBAAI,WAAU;;sCACb,kSAAC;4BAAK,WAAU;sCAAI;;;;;;sCACpB,kSAAC;4BAAK,WAAU;sCAAY;;;;;;;;;;;;;;;;;;;;;;;AAKtC", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/geopin/app/page.tsx"], "sourcesContent": ["import Button from \"@/components/button/button\";\nimport InlineLink from \"@/components/inline_link/inline_link\";\nimport Image from \"next/image\";\n\nexport default function Home() {\n  return (\n    <main className=\"font-sans grid grid-rows-[20px_1fr_20px] items-center justify-items-center min-h-screen p-8 pb-20 gap-16 sm:p-20\">\n      a<Button>Hello</Button>\n      <InlineLink href=\"#\" icon=\"file\" iconSize={32} iconColor=\"yellow-500\">\n        Hello\n      </InlineLink>\n      <InlineLink href=\"#\" icon=\"file\" iconSize={32} iconColor=\"yellow-500\">\n        Hello\n      </InlineLink>\n      <InlineLink href=\"#\" icon=\"file\" iconSize={32} iconColor=\"yellow-500\">\n        Hello\n      </InlineLink>\n      <InlineLink href=\"#\" icon=\"file\" iconSize={32} iconColor=\"yellow-500\">\n        Hello\n      </InlineLink>\n      <InlineLink href=\"#\" icon=\"file\" iconSize={32} iconColor=\"yellow-500\">\n        Hello\n      </InlineLink>\n      <InlineLink href=\"#\" icon=\"file\" iconSize={32} iconColor=\"yellow-500\">\n        Hello\n      </InlineLink>\n      <InlineLink href=\"#\" icon=\"file\" iconSize={32} iconColor=\"yellow-500\">\n        Hello\n      </InlineLink>\n      <InlineLink href=\"#\" icon=\"file\" iconSize={32} iconColor=\"yellow-500\">\n        Hello\n      </InlineLink>\n      <InlineLink href=\"#\" icon=\"file\" iconSize={32} iconColor=\"yellow-500\">\n        Hello\n      </InlineLink>\n      <InlineLink href=\"#\" icon=\"file\" iconSize={32} iconColor=\"yellow-500\">\n        Hello\n      </InlineLink>\n      <InlineLink href=\"#\" icon=\"file\" iconSize={32} iconColor=\"yellow-500\">\n        Hello\n      </InlineLink>\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAGe,SAAS;IACtB,qBACE,kSAAC;QAAK,WAAU;;YAAmH;0BAChI,kSAAC,8LAAM;0BAAC;;;;;;0BACT,kSAAC,wMAAU;gBAAC,MAAK;gBAAI,MAAK;gBAAO,UAAU;gBAAI,WAAU;0BAAa;;;;;;0BAGtE,kSAAC,wMAAU;gBAAC,MAAK;gBAAI,MAAK;gBAAO,UAAU;gBAAI,WAAU;0BAAa;;;;;;0BAGtE,kSAAC,wMAAU;gBAAC,MAAK;gBAAI,MAAK;gBAAO,UAAU;gBAAI,WAAU;0BAAa;;;;;;0BAGtE,kSAAC,wMAAU;gBAAC,MAAK;gBAAI,MAAK;gBAAO,UAAU;gBAAI,WAAU;0BAAa;;;;;;0BAGtE,kSAAC,wMAAU;gBAAC,MAAK;gBAAI,MAAK;gBAAO,UAAU;gBAAI,WAAU;0BAAa;;;;;;0BAGtE,kSAAC,wMAAU;gBAAC,MAAK;gBAAI,MAAK;gBAAO,UAAU;gBAAI,WAAU;0BAAa;;;;;;0BAGtE,kSAAC,wMAAU;gBAAC,MAAK;gBAAI,MAAK;gBAAO,UAAU;gBAAI,WAAU;0BAAa;;;;;;0BAGtE,kSAAC,wMAAU;gBAAC,MAAK;gBAAI,MAAK;gBAAO,UAAU;gBAAI,WAAU;0BAAa;;;;;;0BAGtE,kSAAC,wMAAU;gBAAC,MAAK;gBAAI,MAAK;gBAAO,UAAU;gBAAI,WAAU;0BAAa;;;;;;0BAGtE,kSAAC,wMAAU;gBAAC,MAAK;gBAAI,MAAK;gBAAO,UAAU;gBAAI,WAAU;0BAAa;;;;;;0BAGtE,kSAAC,wMAAU;gBAAC,MAAK;gBAAI,MAAK;gBAAO,UAAU;gBAAI,WAAU;0BAAa;;;;;;;;;;;;AAK5E", "debugId": null}}]}