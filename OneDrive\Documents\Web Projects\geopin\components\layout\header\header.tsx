"use client";


import { useState, useRef } from "react";
import Banner from "../../banner/banner";
import Navbar from "../../navbar/navbar";
import StickyHeader from "./sticky_header/sticky_header";

  const [bannerOpen, setBannerOpen] = useState(true);
  const bannerRef = useRef<HTMLDivElement>(null);
  return (
    <header className="w-full mb-2">
      <Banner onStateChange={setBannerOpen} ref={bannerRef} />
      <StickyHeader bannerOpen={bannerOpen} bannerRef={bannerRef}>
        <Navbar />
      </StickyHeader>
    </header>
  );
}
